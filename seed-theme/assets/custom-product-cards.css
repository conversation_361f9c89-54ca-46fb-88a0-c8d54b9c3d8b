/* Custom Product Card Styles for Collection Pages */

/* Product Title Styling */
.custom-product-title {
  background-color: rgba(0, 0, 0, 0) !important;
  box-sizing: border-box !important;
  color: rgb(74, 74, 74) !important;
  cursor: pointer !important;
  display: inline !important;
  font-family: Lato, sans-serif !important;
  font-size: 20px !important;
  font-weight: 700 !important;
  height: auto !important;
  line-height: 24px !important;
  outline-color: rgb(74, 74, 74) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  text-decoration-color: rgb(74, 74, 74) !important;
  text-decoration-line: none !important;
  text-decoration-style: solid !important;
  text-decoration-thickness: auto !important;
  text-size-adjust: 100% !important;
  width: auto !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
}

/* Price Container Layout */
.custom-price-container {
  display: flex !important;
  align-items: baseline !important;
  flex-wrap: wrap !important;
}

/* Sale Price Styling */
.custom-sale-price {
  box-sizing: border-box !important;
  color: rgb(252, 8, 8) !important;
  display: inline-block !important;
  font-family: Lato, sans-serif !important;
  font-size: 16px !important;
  font-weight: 700 !important;
  line-height: 22.4px !important;
  margin-right: 5px !important;
  outline-color: rgb(252, 8, 8) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  text-size-adjust: 100% !important;
  text-transform: uppercase !important;
  unicode-bidi: isolate !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
}

/* Code Text Styling */
.custom-code-text {
  background-color: rgba(230, 245, 252, 0.5) !important;
  box-sizing: border-box !important;
  color: rgb(74, 74, 74) !important;
  display: inline-block !important;
  font-family: Lato, sans-serif !important;
  font-size: 12px !important;
  font-weight: 400 !important;
  line-height: 21px !important;
  outline-color: rgb(74, 74, 74) !important;
  outline-style: none !important;
  outline-width: 0px !important;
  padding: 0px 1px !important;
  text-size-adjust: 100% !important;
  text-transform: uppercase !important;
  unicode-bidi: isolate !important;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0) !important;
  border-radius: 2px !important;
}

/* Old Price (Strikethrough) Styling */
.custom-price-container .old-price {
  color: rgb(147, 143, 156) !important;
  font-family: Lato, sans-serif !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  line-height: 22.4px !important;
  text-decoration: line-through !important;
  margin-right: 5px !important;
}

/* Hide Sale Labels and View Options Buttons */
.product-card .s1lb .overlay-sale,
.product-card .link-btn,
.product-card .overlay-buy_button {
  display: none !important;
}

/* Hide any hover buttons or overlays */
.product-card:hover .overlay-buy_button,
.product-card:hover .link-btn {
  display: none !important;
}

/* Product Image Height Limitation - Desktop */
.product-card figure {
  height: 280px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  overflow: hidden !important;
  margin-bottom: 8px !important;
}

.product-card figure img,
.product-card figure picture img {
  max-height: 280px !important;
  max-width: 100% !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
  object-position: center !important;
}

.product-card figure picture {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
}

/* Product Card Container Height Reduction */
.product-card,
.l4cl li {
  min-height: auto !important;
  height: auto !important;
}

/* Override default l4cl margins and spacing */
.l4cl {
  margin-bottom: 0 !important;
}

.l4cl li {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* Reduce padding and margins in product cards */
.product-card {
  padding-bottom: 15px !important;
  margin-bottom: 0 !important;
}

/* Reduce spacing between card elements */
.product-card h3,
.product-card .custom-product-title {
  margin-top: 3px !important;
  margin-bottom: 3px !important;
}

.product-card .custom-price-container {
  margin-top: 3px !important;
  margin-bottom: 5px !important;
}

/* Mobile Responsive Adjustments */
@media (max-width: 768px) {
  .custom-product-title {
    font-size: 18px !important;
    line-height: 22px !important;
  }
  
  .custom-sale-price {
    font-size: 15px !important;
  }
  
  .custom-code-text {
    font-size: 14px !important;
    line-height: 19px !important;
  }
  
  .custom-price-container {
    gap: 3px !important;
  }

  /* Mobile Image Height */
  .product-card figure {
    height: 240px !important;
    margin-bottom: 6px !important;
  }

  .product-card figure img,
  .product-card figure picture img {
    max-height: 240px !important;
  }

  /* Mobile spacing adjustments */
  .product-card {
    padding-bottom: 12px !important;
    margin-bottom: 0 !important;
  }

  .l4cl li {
    margin-bottom: 0 !important;
  }
}

@media (max-width: 480px) {
  .custom-product-title {
    font-size: 16px !important;
    line-height: 20px !important;
  }
  
  .custom-sale-price {
    font-size: 14px !important;
  }
  
  .custom-code-text {
    font-size: 13px !important;
    line-height: 18px !important;
  }

  /* Small Mobile Image Height */
  .product-card figure {
    height: 200px !important;
    margin-bottom: 5px !important;
  }

  .product-card figure img,
  .product-card figure picture img {
    max-height: 200px !important;
  }

  /* Small mobile spacing adjustments */
  .product-card {
    padding-bottom: 10px !important;
    margin-bottom: 0 !important;
  }

  .l4cl li {
    margin-bottom: 0 !important;
  }
}
